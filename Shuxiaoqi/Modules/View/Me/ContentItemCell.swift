//
//  ContentItemCell.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/30.
//

import UIKit
import Kingfisher

// MARK: - ContentItemCell
class ContentItemCell: UICollectionViewCell {
    
    // 封面图片
    private let coverImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.backgroundColor = .gray
        return imageView
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#333333")
        label.numberOfLines = 2
        return label
    }()
    
    // 数据标签容器
    private let statsContainer: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.alignment = .center
        return stackView
    }()
    
    // 时间标签
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 12)
        label.textAlignment = .right
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        
        // 添加封面图片
        contentView.addSubview(coverImageView)
        coverImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(300)
        }
        coverImageView.layer.cornerRadius = 16
        
        // 添加时间标签
        coverImageView.addSubview(durationLabel)
        durationLabel.snp.makeConstraints { make in
            make.right.equalTo(-12)
            make.bottom.equalTo(-12)
        }
        
        // 添加标题
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(coverImageView.snp.bottom).offset(12)  // 增加到12pt
            make.left.right.equalToSuperview()
        }
        
        // 创建左右统计视图
        let leftStats = createStatsLabel(icon: "likes_icon", count: "9999")
        let rightStats = createStatsLabel(icon: "views_icon", count: "99")
        
        // 添加统计视图并设置约束
        contentView.addSubview(leftStats)
        contentView.addSubview(rightStats)
        
        leftStats.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(12)  // 增加到12pt
        }
        
        rightStats.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(12)  // 增加到12pt
        }
    }
    
    private func createStatsLabel(icon: String, count: String) -> UIView {
        let container = UIView()
        
        let iconView = UIImageView(image: UIImage(named: icon))
        iconView.contentMode = .scaleAspectFit
        
        let countLabel = UILabel()
        countLabel.text = count
        countLabel.font = .systemFont(ofSize: 12)
        countLabel.textColor = UIColor(hex: "#999999")
        
        container.addSubview(iconView)
        container.addSubview(countLabel)
        
        // 设置固定宽度以防止溢出
        container.snp.makeConstraints { make in
            make.width.lessThanOrEqualTo(60)  // 减小最大宽度
        }
        
        iconView.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
            make.size.equalTo(16)
        }
        
        countLabel.snp.makeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(4)  // 数字距离图标4pt
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualToSuperview()
        }
        
        return container
    }
    
    func configure(with item: ContentItem) {
        titleLabel.text = item.title
        durationLabel.text = item.duration
        if item.worksType == 2 {
            durationLabel.isHidden = true
        } else {
            durationLabel.isHidden = false
        }
        // 添加调试信息
        print("配置Cell - 标题: \(item.title), 图片URL: \(item.coverImage)")
        
        // 使用Kingfisher加载图片
        if let imageUrl = URL(string: item.coverImage) {
            print("准备加载图片: \(imageUrl)")
            coverImageView.kf.setImage(
                with: imageUrl,
                placeholder: UIImage(named: "placeholder_image"),
                options: [.transition(.fade(0.2))],
                completionHandler: { result in
                    switch result {
                    case .success(let value):
                        print("图片加载成功: \(value.source.url?.absoluteString ?? "")")
                    case .failure(let error):
                        print("图片加载失败: \(error.localizedDescription)")
                    }
                }
            )
        } else {
            print("无效的图片URL: \(item.coverImage)")
            coverImageView.image = UIImage(named: "placeholder_image")
        }
        
        updateStats(views: item.views, likes: item.likes)
    }
    
    private func updateStats(views: Int, likes: Int) {
        // 移除旧的统计视图
        contentView.subviews.filter { $0 != coverImageView && $0 != titleLabel && $0 != durationLabel }.forEach { $0.removeFromSuperview() }
        
        // 创建新的统计视图
        let leftStats = createStatsLabel(icon: "likes_icon", count: "\(likes)")
        let rightStats = createStatsLabel(icon: "views_icon", count: "\(views)")
        
        // 添加新的统计视图并设置约束
        contentView.addSubview(leftStats)
        contentView.addSubview(rightStats)
        
        leftStats.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(12)  // 增加到12pt
        }
        
        rightStats.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(12)  // 增加到12pt
        }
    }
}
