//
//  PageSegmentView.swift
//  DouYinSwift5
//
//  Created by lym on 2020/7/28.
//  Copyright © 2020 lym. All rights reserved.
//

import UIKit

// MARK: - 代理协议，用于通知外部标签选择变化
protocol PageSegmentViewDelegate: AnyObject {
    /// 当选择了某个标签时回调
    /// - Parameter index: 被选中标签的索引
    func pageSegment(selectedIndex index: Int)
}

class PageSegmentView: UIView {
    // MARK: - 属性
    /// 所有标题标签的数组
    private var labels: [UILabel] = []
    /// 底部指示器视图
    private var indicateView: UIView!
    /// 指示器视图的中心X轴约束，用于动画
    private var indicateViewCenterX: NSLayoutConstraint!
    /// 当前选中的标签索引
    private var currentTag: Int = 0

    /// 代理对象，用于通知外部标签选择变化
    public weak var delegate: PageSegmentViewDelegate?

    // MARK: - 初始化
    init() {
        super.init(frame: CGRect.zero)
        setUpUI()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置
    private func setUpUI() {
        
        // 创建水平堆栈视图用于均匀排列标签
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        addSubview(stack)
        stack.translatesAutoresizingMaskIntoConstraints = false
        stack.leftAnchor.constraint(equalTo: leftAnchor, constant: 20).isActive = true
        stack.topAnchor.constraint(equalTo: topAnchor).isActive = true
        stack.rightAnchor.constraint(equalTo: rightAnchor, constant: -20).isActive = true
        stack.bottomAnchor.constraint(equalTo: bottomAnchor).isActive = true

        // 创建底部指示器视图
        indicateView = UIView()
        indicateView.backgroundColor = UIColor(hex: "#FF6236")
        addSubview(indicateView)
        indicateView.translatesAutoresizingMaskIntoConstraints = false
        indicateView.bottomAnchor.constraint(equalTo: bottomAnchor).isActive = true
        indicateView.heightAnchor.constraint(equalToConstant: 2).isActive = true

        // 设置标题内容
        let titles = ["作品 0", "喜欢 0", "收藏 0"]
        for (index, title) in titles.enumerated() {
            let titleLab = UILabel()
            titleLab.text = title
            titleLab.textAlignment = .center
            titleLab.font = .boldSystemFont(ofSize: 13)
            titleLab.tag = index // 使用tag存储索引
            labels.append(titleLab)
            stack.addArrangedSubview(titleLab)

            // 设置初始状态：当前选中的标签样式不同
            if index == currentTag {
                titleLab.textColor = UIColor(hex: "#FF6236")
                titleLab.font = .boldSystemFont(ofSize: 13)
                // 初始化指示器位置，与当前选中标签对齐
                indicateViewCenterX = indicateView.centerXAnchor.constraint(equalTo: titleLab.centerXAnchor)
                indicateViewCenterX.isActive = true
                indicateView.widthAnchor.constraint(equalTo: titleLab.widthAnchor).isActive = true
            } else {
                titleLab.textColor = UIColor(hex: "#444444")
                titleLab.font = .boldSystemFont(ofSize: 13)
            }
            
            // 添加标签点击手势
            titleLab.isUserInteractionEnabled = true
            let tap = UITapGestureRecognizer(target: self, action: #selector(tapGestureHandler(tapGes:)))
            titleLab.addGestureRecognizer(tap)
        }
    }

    // MARK: - 标签点击事件处理
    @objc func tapGestureHandler(tapGes: UITapGestureRecognizer) {
        guard let targetLabel = tapGes.view as? UILabel else { return }

        let currentLabel = labels[currentTag]

        // 移除旧的指示器位置约束，准备重新设置
        removeConstraint(indicateViewCenterX)
        // 创建新的约束，指向目标标签
        indicateViewCenterX = indicateView.centerXAnchor.constraint(equalTo: targetLabel.centerXAnchor)
        
        // 动画过渡到新位置
        UIView.animate(withDuration: 0.25, delay: 0, options: .curveEaseInOut, animations: {
            self.indicateViewCenterX.isActive = true
            self.layoutIfNeeded() // 触发约束更新和动画
        }, completion: { if $0 {
            // 动画完成后更新标签样式
            currentLabel.textColor = UIColor(hex: "#444444")
            currentLabel.font = .boldSystemFont(ofSize: 13)
            targetLabel.textColor = UIColor(hex: "#FF6236")
            targetLabel.font = .boldSystemFont(ofSize: 13)
            self.currentTag = targetLabel.tag
        } })
        
        // 通知代理选择已更改
        delegate?.pageSegment(selectedIndex: targetLabel.tag)
    }

    // MARK: - 页面滑动时更新指示器位置和标签样式
    /// 当容器视图进行滑动时，更新标题栏的状态
    /// - Parameters:
    ///   - progress: 滑动进度，范围0-1
    ///   - sourceIndex: 滑动开始的标签索引
    ///   - targetIndex: 滑动目标的标签索引
    func setTitle(progress: CGFloat, sourceIndex: Int, targetIndex: Int) {
        // 移除旧的指示器位置约束
        removeConstraint(indicateViewCenterX)
        let sourceLabel = labels[sourceIndex]
        let targetLabel = labels[targetIndex]

        // 计算指示器需要移动的总距离
        let totalDistance = targetLabel.centerX - sourceLabel.centerX

        // 根据滑动进度设置指示器的位置
        removeConstraint(indicateViewCenterX)
        indicateViewCenterX = indicateView.centerXAnchor.constraint(equalTo: sourceLabel.centerXAnchor, constant: totalDistance * progress)
        indicateViewCenterX.isActive = true

        // 当滑动完成时(progress=1)，更新标签样式和当前选中索引
        if progress == 1 {
            if sourceIndex == targetIndex {
                let currentLabel = labels[currentTag]
                currentLabel.textColor = UIColor(hex: "#444444")
                currentLabel.font = .boldSystemFont(ofSize: 13)
            } else {
                sourceLabel.textColor = UIColor(hex: "#444444")
                sourceLabel.font = .boldSystemFont(ofSize: 13)
            }
            targetLabel.textColor = UIColor(hex: "#FF6236")
            targetLabel.font = .boldSystemFont(ofSize: 13)
            currentTag = targetIndex
        }
    }

    // 新增：动态设置segment标题
    public func setSegmentTitles(_ titles: [String]) {
        guard let stack = subviews.first(where: { $0 is UIStackView }) as? UIStackView else { return }
        // 清空原有labels和stack内容
        labels.removeAll()
        stack.arrangedSubviews.forEach { stack.removeArrangedSubview($0); $0.removeFromSuperview() }
        for (index, title) in titles.enumerated() {
            let titleLab = UILabel()
            titleLab.text = title
            titleLab.textAlignment = .center
            titleLab.font = .boldSystemFont(ofSize: 13)
            titleLab.tag = index
            labels.append(titleLab)
            stack.addArrangedSubview(titleLab)
            if index == currentTag {
                titleLab.textColor = UIColor(hex: "#FF6236")
                titleLab.font = .boldSystemFont(ofSize: 13)
                // 重新设置指示器位置
                if indicateViewCenterX != nil { removeConstraint(indicateViewCenterX) }
                indicateViewCenterX = indicateView.centerXAnchor.constraint(equalTo: titleLab.centerXAnchor)
                indicateViewCenterX.isActive = true
                indicateView.widthAnchor.constraint(equalTo: titleLab.widthAnchor).isActive = true
            } else {
                titleLab.textColor = UIColor(hex: "#444444")
                titleLab.font = .boldSystemFont(ofSize: 13)
            }
            // 添加点击手势
            titleLab.isUserInteractionEnabled = true
            let tap = UITapGestureRecognizer(target: self, action: #selector(tapGestureHandler(tapGes:)))
            titleLab.addGestureRecognizer(tap)
        }
        layoutIfNeeded()
    }

    /// 获取当前的segment标题
    public func getCurrentTitles() -> [String] {
        return labels.map { $0.text ?? "" }
    }
}
